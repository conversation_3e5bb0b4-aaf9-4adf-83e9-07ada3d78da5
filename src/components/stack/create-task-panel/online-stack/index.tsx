import { <PERSON><PERSON> } from "@/components/ui/button";
import { Arrow<PERSON><PERSON><PERSON>, Loader2, Trash2, VideoIcon, Edit3 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useMemo } from "react";
import { OnlineStackProps, VideoUrlFormData, videoUrlSchema } from "./types";
import { useVideoInfo } from "@/hooks/swr/use-video-info";
import FormGenerator from "@/components/common/form-generator";
import { FormatButton } from "./components/format-selection/format-button";
import {
  currentTaskAtom,
  resetCurrentTaskAtom,
  showPreviewSubtitleAtom,
  showVideoPreviewPanelAtom,
} from "@/stores/slices/current_task";
import { subtitleDataAtom } from "@/stores/slices/subtitle_store";
import { useAtom, useSetAtom, useAtomValue } from "jotai";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { getBestFormats } from "@/utils/video-format";
import { TaskSettings } from "./components/task-settings";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useTaskManager } from "@/hooks/task/use-task-manager";
import { toast } from "sonner";
import { createScopedLogger } from "@/utils";
import { apiKy } from "@/api";
import { env } from "@/env";

const logger = createScopedLogger("online-stack");

// Helper function to generate SRT content from subtitle data
const generateSrtContent = (subtitleData: any) => {
  const { originalSubtitles, translatedSubtitles } = subtitleData;

  return originalSubtitles
    .map((sub: any, index: number) => {
      const translatedSub = translatedSubtitles[index];

      // Format: index, time range, original text, translated text (if available)
      let content = `${index + 1}\n${sub.startTime} --> ${sub.endTime}\n${sub.text}`;

      if (translatedSub && translatedSub.translatedText) {
        content += `\n${translatedSub.translatedText}`;
      }

      return content;
    })
    .join('\n\n');
};

// Helper function to upload SRT file and get URL
const uploadSrtFile = async (srtContent: string): Promise<string> => {
  const apiHost = env.NEXT_PUBLIC_BASE_API_HOST || 'http://127.0.0.1:9011';

  // Create a blob from the SRT content
  const blob = new Blob([srtContent], { type: 'text/plain' });
  const file = new File([blob], 'subtitles.srt', { type: 'text/plain' });

  // Create form data for upload
  const formData = new FormData();
  formData.append('file', file);

  // Upload the file
  const response = await fetch(`${apiHost}/upload`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`Upload failed: ${response.statusText}`);
  }

  const result = await response.json();

  if (result.code !== 0) {
    throw new Error(`Upload failed: ${result.msg || 'Unknown error'}`);
  }

  return result.data.url;
};

// Helper function to upload thumbnail image and get URL
const uploadThumbnailFile = async (thumbnailUrl: string): Promise<string> => {
  const apiHost = env.NEXT_PUBLIC_BASE_API_HOST || 'http://127.0.0.1:9011';

  // Check if it's already a regular URL (not base64)
  if (thumbnailUrl.startsWith('http://') || thumbnailUrl.startsWith('https://')) {
    return thumbnailUrl; // Return as-is if it's already a URL
  }

  // Handle base64 data URLs
  if (thumbnailUrl.startsWith('data:')) {
    // Extract the base64 data and mime type
    const [header, base64Data] = thumbnailUrl.split(',');
    const mimeMatch = header.match(/data:([^;]+)/);
    const mimeType = mimeMatch ? mimeMatch[1] : 'image/jpeg';

    // Convert base64 to blob
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: mimeType });

    // Create file with appropriate extension
    const extension = mimeType.split('/')[1] || 'jpg';
    const file = new File([blob], `thumbnail.${extension}`, { type: mimeType });

    // Create form data for upload
    const formData = new FormData();
    formData.append('file', file);

    // Upload the file
    const response = await fetch(`${apiHost}/upload`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Thumbnail upload failed: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.code !== 0) {
      throw new Error(`Thumbnail upload failed: ${result.msg || 'Unknown error'}`);
    }

    return result.data.url;
  }

  // If it's neither a URL nor base64, return as-is
  return thumbnailUrl;
};

export const OnlineStack = ({ stackRef }: OnlineStackProps) => {
  const t = useTranslations();
  const [currentTask, setCurrentTask] = useAtom(currentTaskAtom);
  const resetCurrentTask = useSetAtom(resetCurrentTaskAtom);
  const [showPreviewSubtitle, setShowPreviewSubtitle] = useAtom(
    showPreviewSubtitleAtom
  );
  const [, setShowVideoPreviewPanel] = useAtom(showVideoPreviewPanelAtom);
  const subtitleData = useAtomValue(subtitleDataAtom);
  const { createTask } = useTaskManager();

  const {
    watch,
    register,
    handleSubmit,
    setValue: setValueForm,
    formState: { errors, isSubmitting },
  } = useForm<VideoUrlFormData>({
    defaultValues: { videoUrl: currentTask.videoUrl },
    resolver: zodResolver(videoUrlSchema, {
      errorMap: (issue) => ({
        message:
          issue.code === "too_small"
            ? t("form.errors.required")
            : t("form.errors.invalidVideoUrl"),
      }),
    }),
  });

  const {
    data: videoInfo,
    error,
    trigger: getVideoInfo,
    isMutating: isLoading,
  } = useVideoInfo();

  // Check if current task is an uploaded video
  const isUploadedVideo = !!(
    currentTask.name &&
    currentTask.duration &&
    currentTask.thumbnail
  );

  // Get the best quality formats from available formats
  const bestFormats = useMemo(() => {
    return videoInfo?.info?.formats
      ? getBestFormats(videoInfo.info.formats)
      : [];
  }, [videoInfo]);

  // Auto-select the first format when video info is loaded
  useEffect(() => {
    if (
      videoInfo?.info &&
      bestFormats.length > 0 &&
      !currentTask.settings.selectedFormat
    ) {
      setCurrentTask((prev) => ({
        ...prev,
        settings: {
          ...prev.settings,
          selectedFormat: bestFormats[0],
        },
      }));
      toast.success(t("global.video.formatSelected"));
    }
  }, [
    videoInfo,
    bestFormats,
    currentTask.settings.selectedFormat,
    setCurrentTask,
    t,
  ]);

  // Show/hide video preview panel based on video availability
  useEffect(() => {
    const hasVideoData = !!(videoInfo?.info || isUploadedVideo);
    setShowVideoPreviewPanel(hasVideoData);

    // Hide video preview panel when component unmounts
    return () => {
      setShowVideoPreviewPanel(false);
    };
  }, [videoInfo?.info, isUploadedVideo, setShowVideoPreviewPanel]);

  // Handle video URL form submission
  const handleFormSubmit = useCallback(
    (data: VideoUrlFormData) => {
      setCurrentTask((prev) => ({ ...prev, videoUrl: data.videoUrl }));
      getVideoInfo({ videoUrl: data.videoUrl });
      toast.info(t("global.video.gettingInfo"));
    },
    [setCurrentTask, getVideoInfo, t]
  );

  // Handle task creation confirmation
  const handleConfirm = useCallback(async () => {
    setShowVideoPreviewPanel(false);
    stackRef.current?.pop();

    try {
      let subtitleUrl = null;
      let thumbnailUrl = null;

      // Upload subtitles if they exist
      if (subtitleData.originalSubtitles.length > 0) {
        logger.info("Uploading subtitles before sending task", {
          taskId: currentTask.id,
          subtitleCount: subtitleData.originalSubtitles.length,
        });

        // Generate SRT content from subtitles
        const srtContent = generateSrtContent(subtitleData);

        // Upload SRT file and get URL
        subtitleUrl = await uploadSrtFile(srtContent);

        logger.info("Subtitles uploaded successfully", {
          taskId: currentTask.id,
          subtitleUrl,
        });
      }

      // Upload thumbnail if it exists and is base64
      const originalThumbnail = currentTask.thumbnail || videoInfo?.info?.thumbnail;
      if (originalThumbnail) {
        logger.info("Processing thumbnail for upload", {
          taskId: currentTask.id,
          thumbnailType: originalThumbnail.startsWith('data:') ? 'base64' : 'url',
          thumbnailSize: originalThumbnail.length,
        });

        // Upload thumbnail and get URL
        thumbnailUrl = await uploadThumbnailFile(originalThumbnail);

        logger.info("Thumbnail processed successfully", {
          taskId: currentTask.id,
          thumbnailUrl,
          originalSize: originalThumbnail.length,
          newSize: thumbnailUrl.length,
        });
      }

      // Send task settings and subtitle URL to /trans_video endpoint
      logger.info("Sending task data to /trans_video endpoint", {
        taskId: currentTask.id,
        settings: currentTask.settings,
        subtitleUrl,
        thumbnailUrl,
      });

      const response = await apiKy.post("trans_video", {
        json: {
          task: {
            id: currentTask.id,
            videoUrl: currentTask.videoUrl,
            name: currentTask.name || videoInfo?.info?.title,
            duration: currentTask.duration || videoInfo?.info?.duration,
            thumbnail: thumbnailUrl || currentTask.thumbnail || videoInfo?.info?.thumbnail,
            settings: currentTask.settings,
          },
          subtitle_url: subtitleUrl,
        },
      });

      logger.info("Successfully sent data to /trans_video endpoint", {
        taskId: currentTask.id,
        response: await response.json(),
      });

      // For uploaded videos, use existing task info
      if (currentTask.name && currentTask.duration && currentTask.thumbnail) {
        await createTask({
          ...currentTask,
          thumbnail: thumbnailUrl || currentTask.thumbnail,
          status: "pending" as const,
          progress: 0,
        });
      } else if (currentTask.settings.selectedFormat && videoInfo?.info) {
        // For online videos, create task with fetched info
        await createTask({
          ...currentTask,
          name: videoInfo.info.title,
          duration: videoInfo.info.duration,
          thumbnail: thumbnailUrl || videoInfo.info.thumbnail,
          status: "pending" as const,
          progress: 0,
        });
      }

      toast.success(t("global.video.taskCreated"));
    } catch (error) {
      logger.error("Failed to send data to /trans_video endpoint", {
        taskId: currentTask.id,
        error: error instanceof Error ? error.message : String(error),
      });

      toast.error(`Failed to submit task: ${error instanceof Error ? error.message : String(error)}`);
      return; // Don't reset task if there was an error
    }

    resetCurrentTask();
  }, [
    currentTask,
    videoInfo,
    stackRef,
    createTask,
    resetCurrentTask,
    setShowVideoPreviewPanel,
    subtitleData,
    t,
  ]);

  // Handle task removal
  const handleRemove = useCallback(() => {
    setShowVideoPreviewPanel(false);
    resetCurrentTask();
    stackRef.current?.pop();
    toast.success(t("global.video.taskRemoved"));
  }, [resetCurrentTask, stackRef, setShowVideoPreviewPanel, t]);

  return (
    <div className="flex size-full flex-col">
      <div className="flex items-center gap-2 border-b px-3 py-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => {
            setShowVideoPreviewPanel(false);
            stackRef.current?.pop();
          }}
          className="size-6"
        >
          <ArrowLeft className="size-3.5" />
        </Button>
        <span className="text-xs font-medium">
          {isUploadedVideo
            ? t("global.video.uploadedVideo")
            : t("global.video.onlineVideo")}
        </span>
        <div className="flex-1" />
        <Button
          variant="ghost"
          size="icon"
          onClick={handleRemove}
          className="size-6 text-destructive hover:text-destructive"
        >
          <Trash2 className="size-3.5" />
        </Button>
      </div>

      <ScrollArea className="flex-1">
        <div className="flex flex-col gap-3 p-3">
          {!isUploadedVideo && (
            <form
              onSubmit={handleSubmit(handleFormSubmit)}
              className="space-y-2"
            >
              <div className="flex flex-col items-end gap-2">
                <div className="w-full flex-1">
                  <FormGenerator
                    id="videoUrl"
                    inputType="input"
                    name="videoUrl"
                    errors={errors}
                    register={register}
                    setValue={setValueForm}
                    watch={watch}
                    label={t("form.fields.videoUrl.label")}
                    placeholder={t("form.fields.videoUrl.placeholder")}
                  />
                </div>
                <Button
                  type="submit"
                  disabled={isSubmitting || isLoading}
                  className="max-w-fit"
                  size="sm"
                >
                  {isSubmitting || isLoading ? (
                    <Loader2 className="mr-1.5 size-3.5 animate-spin" />
                  ) : (
                    <VideoIcon className="mr-1.5 size-3.5" />
                  )}
                  {t("form.get_video_info")}
                </Button>
              </div>

              {error?.message && (
                <p className="text-xs text-destructive">{error.message}</p>
              )}
            </form>
          )}

          {/* Subtitle Editor Option */}
          <div className="space-y-2">
            <div className="flex items-center justify-between px-1">
              <span className="text-xs font-medium text-muted-foreground">
                Subtitle Editor
              </span>
            </div>
            <Button
              variant="outline"
              className="w-full"
              size="sm"
              onClick={() => stackRef.current?.push("subtitle-editor-with-video")}
            >
              <Edit3 className="mr-1.5 size-3.5" />
              Open Subtitle Editor
            </Button>
          </div>

          {(videoInfo?.info || isUploadedVideo) && (
            <div className="space-y-3">
              <div className="space-y-3">
                {!isUploadedVideo && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between px-1">
                      <span className="text-xs font-medium text-muted-foreground">
                        {t("form.fields.formats")}
                      </span>
                      <span className="text-[0.7rem] text-muted-foreground">
                        {bestFormats.length} {t("form.fields.formatsAvailable")}
                      </span>
                    </div>
                    <div className="grid gap-1.5 @sm:grid-cols-2">
                      {bestFormats.map((format) => (
                        <FormatButton
                          key={format.formatId}
                          format={format}
                          isSelected={
                            currentTask.settings.selectedFormat?.formatId ===
                            format.formatId
                          }
                          onSelect={() => {
                            setCurrentTask((prev) => ({
                              ...prev,
                              settings: {
                                ...prev.settings,
                                selectedFormat: format,
                              },
                            }));
                            toast.success(t("global.video.formatSelected"));
                          }}
                        />
                      ))}
                    </div>
                  </div>
                )}

                <div className="rounded-lg border bg-card p-3">
                  <TaskSettings
                    voiceSeparation={currentTask.settings.voiceSeparation}
                    sourceLanguage={currentTask.settings.sourceLanguage}
                    targetLanguage={currentTask.settings.targetLanguage}
                    subtitleLayout={currentTask.settings.subtitleLayout}
                    subtitleStyle={currentTask.settings.subtitleStyle}
                    showSubtitle={showPreviewSubtitle}
                    voiceDubbingEnabled={currentTask.settings.voiceDubbing.enabled}
                    voiceDubbingService={currentTask.settings.voiceDubbing.service}
                    voiceDubbingVoiceName={currentTask.settings.voiceDubbing.voiceName}
                    onVoiceSeparationChange={(checked) => {
                      setCurrentTask((prev) => ({
                        ...prev,
                        settings: {
                          ...prev.settings,
                          voiceSeparation: checked,
                        },
                      }));
                    }}
                    onSourceLanguageChange={(value) => {
                      setCurrentTask((prev) => ({
                        ...prev,
                        settings: {
                          ...prev.settings,
                          sourceLanguage: value,
                        },
                      }));
                    }}
                    onTargetLanguageChange={(value) => {
                      setCurrentTask((prev) => ({
                        ...prev,
                        settings: {
                          ...prev.settings,
                          targetLanguage: value,
                        },
                      }));
                    }}
                    onSubtitleLayoutChange={(value) => {
                      setCurrentTask((prev) => ({
                        ...prev,
                        settings: {
                          ...prev.settings,
                          subtitleLayout: value,
                        },
                      }));
                    }}
                    onSubtitleStyleChange={(updates) => {
                      setCurrentTask((prev) => ({
                        ...prev,
                        settings: {
                          ...prev.settings,
                          subtitleStyle: {
                            ...prev.settings.subtitleStyle,
                            ...updates,
                          },
                        },
                      }));
                    }}
                    onShowSubtitleChange={(show) =>
                      setShowPreviewSubtitle(show)
                    }
                    onVoiceDubbingEnabledChange={(enabled) => {
                      setCurrentTask((prev) => ({
                        ...prev,
                        settings: {
                          ...prev.settings,
                          voiceDubbing: {
                            ...prev.settings.voiceDubbing,
                            enabled,
                          },
                        },
                      }));
                    }}
                    onVoiceDubbingServiceChange={(service) => {
                      setCurrentTask((prev) => ({
                        ...prev,
                        settings: {
                          ...prev.settings,
                          voiceDubbing: {
                            ...prev.settings.voiceDubbing,
                            service,
                            voiceName: "", // Reset voice when service changes
                          },
                        },
                      }));
                    }}
                    onVoiceDubbingVoiceNameChange={(voiceName) => {
                      setCurrentTask((prev) => ({
                        ...prev,
                        settings: {
                          ...prev.settings,
                          voiceDubbing: {
                            ...prev.settings.voiceDubbing,
                            voiceName,
                          },
                        },
                      }));
                    }}
                  />
                </div>
              </div>

              <Button
                className="w-full"
                onClick={handleConfirm}
                disabled={
                  !isUploadedVideo && !currentTask.settings.selectedFormat
                }
                size="sm"
              >
                {t("global.video.confirmSelection")}
              </Button>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};
